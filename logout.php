<?php
session_start();
require_once 'database/conn.php';

// Log the logout action
if (isset($_SESSION['user_id'])) {
    error_log("User logout: ID {$_SESSION['user_id']}, Username: {$_SESSION['username']}");
    
    // Clear remember me token if it exists
    if (isset($_COOKIE['remember_token'])) {
        try {
            $stmt = $pdo->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            
            // Delete the cookie
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        } catch (Exception $e) {
            error_log("Error clearing remember token: " . $e->getMessage());
        }
    }
}

// Destroy all session data
$_SESSION = array();

// Delete session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Redirect to login page
header('Location: login.php?message=logged_out');
exit();
?>

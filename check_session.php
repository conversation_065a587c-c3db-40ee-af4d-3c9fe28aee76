<?php
/**
 * Session Check and Authentication Helper
 * Include this file at the top of protected pages
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Check if user has specific role
function hasRole($required_role) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['user_type'] ?? '';
    
    // Define role hierarchy (higher number = more permissions)
    $role_hierarchy = [
        'Customer' => 1,
        'Vendor' => 2,
        'Staff' => 3,
        'Manager' => 4,
        'Administrator' => 5
    ];
    
    $user_level = $role_hierarchy[$user_role] ?? 0;
    $required_level = $role_hierarchy[$required_role] ?? 0;
    
    return $user_level >= $required_level;
}

// Redirect to login if not authenticated
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        exit();
    }
}

// Redirect to login if user doesn't have required role
function requireRole($required_role) {
    requireLogin();
    
    if (!hasRole($required_role)) {
        header('Location: login.php?error=insufficient_permissions');
        exit();
    }
}

// Get current user information
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'name' => $_SESSION['user_name'],
        'username' => $_SESSION['username'],
        'email' => $_SESSION['user_email'],
        'user_type' => $_SESSION['user_type'],
        'login_time' => $_SESSION['login_time']
    ];
}

// Check session timeout (optional - 2 hours default)
function checkSessionTimeout($timeout_minutes = 120) {
    if (isLoggedIn()) {
        $login_time = $_SESSION['login_time'] ?? time();
        $current_time = time();
        
        if (($current_time - $login_time) > ($timeout_minutes * 60)) {
            // Session expired
            session_destroy();
            header('Location: login.php?error=session_expired');
            exit();
        }
        
        // Update login time to extend session
        $_SESSION['login_time'] = $current_time;
    }
}

// Auto-check session timeout on every page load
checkSessionTimeout();
?>

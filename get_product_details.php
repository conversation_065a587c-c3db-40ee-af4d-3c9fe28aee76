<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in
requireLogin();

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Product ID not provided.'
    ]);
    exit;
}

$productId = intval($_GET['id']);

if ($productId <= 0) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid Product ID.'
    ]);
    exit;
}

try {
    // Select all necessary fields - using 'created' column as seen in main query
    $stmt = $pdo->prepare("SELECT id, product, category, stock, price, status, package, description, created FROM products WHERE id = ?");
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($product) {
        // Convert numeric types
        $product['stock'] = (int)$product['stock'];
        $product['price'] = (float)$product['price'];

        // Add formatted fields that JavaScript expects
        $product['initials'] = strtoupper(substr($product['product'], 0, 2));
        $product['formatted_stock'] = number_format($product['stock']) . ' units';
        $product['formatted_price'] = '$' . number_format($product['price'], 2);

        // Format dates
        if ($product['created']) {
            $product['formatted_created'] = date('M j, Y g:i A', strtotime($product['created']));
        } else {
            $product['formatted_created'] = 'N/A';
        }

        // For updated, we'll use created date as fallback since updated_at might not exist
        $product['formatted_updated'] = $product['formatted_created'];

        // Add status color class
        switch ($product['status']) {
            case 'In Stock':
                $product['status_color'] = 'bg-green-100 text-green-800';
                break;
            case 'Low Stock':
                $product['status_color'] = 'bg-yellow-100 text-yellow-800';
                break;
            case 'Out of Stock':
                $product['status_color'] = 'bg-red-100 text-red-800';
                break;
            default:
                $product['status_color'] = 'bg-gray-100 text-gray-800';
        }

        // Ensure package and description are not null
        $product['package'] = $product['package'] ?: 'Not specified';
        $product['description'] = $product['description'] ?: 'No description available';

        echo json_encode([
            'success' => true,
            'product' => $product
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Product not found.'
        ]);
    }
} catch (PDOException $e) {
    error_log("Database error in get_product_details.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Database error occurred: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    error_log("General error in get_product_details.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'An unexpected error occurred: ' . $e->getMessage()
    ]);
}
?>

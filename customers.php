<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers - Meat Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="css/styles.css"> <!-- Assuming you have a styles.css -->
</head>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div :class="{'translate-x-0 ease-out': sidebarOpen, '-translate-x-full ease-in': !sidebarOpen}" class="fixed inset-y-0 left-0 z-30 w-64 bg-gray-800 text-white transform transition duration-300 lg:translate-x-0 lg:static lg:inset-0">
            <div class="p-4 border-b border-gray-700 flex justify-between items-center lg:justify-start">
                <div>
                    <h1 class="text-2xl font-bold">MeatMS</h1>
                    <p class="text-sm text-gray-400">Admin Panel</p>
                </div>
                <button @click="sidebarOpen = false" class="text-gray-400 hover:text-white lg:hidden">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <nav class="mt-4">
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold">Main</div>
                <a href="index.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>
                
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Management</div>
                <a href="products.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-box mr-2"></i> Products
                </a>
                <a href="orders.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-shopping-cart mr-2"></i> Orders
                </a>
                <a href="users.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-users mr-2"></i> Users
                </a>
                <a href="vendors.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-store mr-2"></i> Vendors
                </a>
                <a href="customers.html" class="block px-4 py-2 bg-indigo-700 text-white rounded mx-2"> <!-- Active link -->
                    <i class="fas fa-address-book mr-2"></i> Customers
                </a>
                
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Sales & Reports</div>
                <a href="sales.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-chart-line mr-2"></i> Sales
                </a>
                <a href="reports.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-file-alt mr-2"></i> Reports
                </a>

                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Financial</div>
                <a href="payments.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-credit-card mr-2"></i> Payments
                </a>
            </nav>
        </div>

        <!-- Overlay for mobile sidebar -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-20 bg-black opacity-50 transition-opacity lg:hidden" x-cloak></div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = !sidebarOpen" class="text-gray-500 focus:outline-none lg:hidden">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold text-gray-800 ml-2">Customers</h2>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Search customers..." class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <button class="p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell"></i>
                        </button>
                        <div class="relative" x-data="{ dropdownOpen: false }">
                            <button @click="dropdownOpen = !dropdownOpen" class="flex items-center space-x-2 focus:outline-none">
                                <img class="w-8 h-8 rounded-full" src="https://via.placeholder.com/32" alt="User">
                                <span class="text-gray-700">Admin</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div x-show="dropdownOpen" @click.away="dropdownOpen = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-20" x-cloak>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Customer Management</h1>
                        <p class="mt-1 text-sm text-gray-600">View and manage your customers</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-user-plus mr-2"></i> Add New Customer
                        </button>
                    </div>
                </div>

                <!-- Customer Stats -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Customers</p>
                                <h3 class="text-xl font-semibold">1,450</h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Active Customers</p>
                                <h3 class="text-xl font-semibold">1,280</h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-user-clock"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">New This Month</p>
                                <h3 class="text-xl font-semibold">75</h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Loyal Customers</p>
                                <h3 class="text-xl font-semibold">320</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Status</option>
                                <option>Active</option>
                                <option>Inactive</option>
                                <option>Blocked</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Registration Date</label>
                            <input type="date" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Customer Group</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Groups</option>
                                <option>Retail</option>
                                <option>Wholesale</option>
                                <option>VIP</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button class="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">All Customers</h3>
                        <div class="flex items-center space-x-2">
                            <button class="text-gray-500 hover:text-gray-700" title="Export Data">
                                <i class="fas fa-file-export"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Sample Row 1 -->
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <img class="h-10 w-10 rounded-full object-cover" src="https://via.placeholder.com/40/FFA500/FFFFFF?Text=JD" alt="John Doe">
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">John Doe</div>
                                                <div class="text-sm text-gray-500">CUST-001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><EMAIL></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">(555) 123-4567</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$1,250.75</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Edit"><i class="fas fa-edit"></i></a>
                                        <a href="#" class="text-red-600 hover:text-red-900" title="Delete"><i class="fas fa-trash"></i></a>
                                    </td>
                                </tr>
                                <!-- Sample Row 2 -->
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <img class="h-10 w-10 rounded-full object-cover" src="https://via.placeholder.com/40/4682B4/FFFFFF?Text=JS" alt="Jane Smith">
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">Jane Smith</div>
                                                <div class="text-sm text-gray-500">CUST-002</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><EMAIL></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">(555) 987-6543</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$850.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Inactive
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Edit"><i class="fas fa-edit"></i></a>
                                        <a href="#" class="text-red-600 hover:text-red-900" title="Delete"><i class="fas fa-trash"></i></a>
                                    </td>
                                </tr>
                                <!-- Add more sample rows as needed -->
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium">1</span> to <span class="font-medium">10</span> of <span class="font-medium">97</span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </a>
                                    <a href="#" aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">2</a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">3</a>
                                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">8</a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">9</a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">10</a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <!-- <script src="js/main.js"></script> -->
</body>
</html>
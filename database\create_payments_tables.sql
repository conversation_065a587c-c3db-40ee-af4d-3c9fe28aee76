-- Create payments and related tables for meat management system
-- Run this SQL in your MySQL database to create the required tables

-- Create orders table
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(50) NOT NULL UNIQUE,
  `customer_id` int(11) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','processing','completed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `order_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON>EY `idx_order_number` (`order_number`),
  <PERSON><PERSON><PERSON> `idx_customer_id` (`customer_id`),
  <PERSON><PERSON><PERSON> `idx_status` (`status`),
  KEY `idx_order_date` (`order_date`),
  FOREIGN KEY (`customer_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create order_items table
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create payments table
CREATE TABLE IF NOT EXISTS `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(100) NOT NULL UNIQUE,
  `order_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` enum('credit_card','paypal','bank_transfer','cash','mobile_money') NOT NULL,
  `status` enum('pending','completed','failed','refunded','cancelled') NOT NULL DEFAULT 'pending',
  `payment_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reference_number` varchar(100) DEFAULT NULL,
  `gateway_response` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_payment_date` (`payment_date`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`customer_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing

-- Sample customers (if not already exist)
INSERT IGNORE INTO `users` (`name`, `username`, `password`, `email`, `user_type`, `phone`, `address`, `created_at`, `updated_at`) VALUES
('John Doe', 'john.doe', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Customer', '(*************', '123 Main St, City, State', NOW(), NOW()),
('Jane Smith', 'jane.smith', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Customer', '(*************', '456 Oak Ave, City, State', NOW(), NOW()),
('Mike Brown', 'mike.brown', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Customer', '(*************', '789 Pine Rd, City, State', NOW(), NOW()),
('Alice Green', 'alice.green', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Customer', '(*************', '321 Elm St, City, State', NOW(), NOW());

-- Sample products (if not already exist)
INSERT IGNORE INTO `products` (`product`, `category`, `stock`, `price`, `status`, `package`, `description`, `created`, `updated_at`) VALUES
('Premium Beef Steak', 'Beef', 50, 25.99, 'Available', '1 lb', 'High-quality beef steak, perfect for grilling', NOW(), NOW()),
('Fresh Chicken Breast', 'Chicken', 100, 12.99, 'Available', '1 lb', 'Fresh, boneless chicken breast', NOW(), NOW()),
('Lamb Chops', 'Lamb', 30, 35.99, 'Available', '1 lb', 'Tender lamb chops, grass-fed', NOW(), NOW()),
('Ground Turkey', 'Turkey', 75, 8.99, 'Available', '1 lb', 'Lean ground turkey meat', NOW(), NOW());

-- Sample orders
INSERT INTO `orders` (`order_number`, `customer_id`, `total_amount`, `status`, `order_date`, `created_at`, `updated_at`) VALUES
('ORD-001', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 150.00, 'completed', '2024-01-15 10:30:00', NOW(), NOW()),
('ORD-002', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 75.50, 'completed', '2024-01-16 14:20:00', NOW(), NOW()),
('ORD-003', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 200.00, 'processing', '2024-01-17 09:15:00', NOW(), NOW()),
('ORD-004', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 50.00, 'completed', '2024-01-18 16:45:00', NOW(), NOW());

-- Sample order items
INSERT INTO `order_items` (`order_id`, `product_id`, `quantity`, `price`, `subtotal`) VALUES
((SELECT id FROM orders WHERE order_number = 'ORD-001'), (SELECT id FROM products WHERE product = 'Premium Beef Steak' LIMIT 1), 2, 25.99, 51.98),
((SELECT id FROM orders WHERE order_number = 'ORD-001'), (SELECT id FROM products WHERE product = 'Fresh Chicken Breast' LIMIT 1), 3, 12.99, 38.97),
((SELECT id FROM orders WHERE order_number = 'ORD-002'), (SELECT id FROM products WHERE product = 'Ground Turkey' LIMIT 1), 2, 8.99, 17.98),
((SELECT id FROM orders WHERE order_number = 'ORD-003'), (SELECT id FROM products WHERE product = 'Lamb Chops' LIMIT 1), 3, 35.99, 107.97),
((SELECT id FROM orders WHERE order_number = 'ORD-004'), (SELECT id FROM products WHERE product = 'Fresh Chicken Breast' LIMIT 1), 1, 12.99, 12.99);

-- Sample payments
INSERT INTO `payments` (`transaction_id`, `order_id`, `customer_id`, `amount`, `payment_method`, `status`, `payment_date`, `reference_number`, `created_at`, `updated_at`) VALUES
('TXN-00123', (SELECT id FROM orders WHERE order_number = 'ORD-001'), (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 150.00, 'credit_card', 'completed', '2024-01-15 10:35:00', 'CC-REF-001', NOW(), NOW()),
('TXN-00124', (SELECT id FROM orders WHERE order_number = 'ORD-002'), (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 75.50, 'paypal', 'failed', '2024-01-16 14:25:00', 'PP-REF-002', NOW(), NOW()),
('TXN-00125', (SELECT id FROM orders WHERE order_number = 'ORD-003'), (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 200.00, 'bank_transfer', 'pending', '2024-01-17 09:20:00', 'BT-REF-003', NOW(), NOW()),
('TXN-00120', (SELECT id FROM orders WHERE order_number = 'ORD-004'), (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 50.00, 'credit_card', 'refunded', '2024-01-18 16:50:00', 'CC-REF-004', NOW(), NOW());

-- Additional sample payments for better testing
INSERT INTO `payments` (`transaction_id`, `customer_id`, `amount`, `payment_method`, `status`, `payment_date`, `notes`, `created_at`, `updated_at`) VALUES
('TXN-00126', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 89.99, 'cash', 'completed', '2024-01-19 11:00:00', 'Walk-in purchase', NOW(), NOW()),
('TXN-00127', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 125.50, 'mobile_money', 'completed', '2024-01-20 15:30:00', 'Mobile payment', NOW(), NOW()),
('TXN-00128', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), 45.75, 'paypal', 'failed', '2024-01-21 13:15:00', 'Payment declined', NOW(), NOW());

<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in
requireLogin();

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Order ID not provided.'
    ]);
    exit;
}

$orderId = intval($_GET['id']);

if ($orderId <= 0) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid Order ID.'
    ]);
    exit;
}

try {
    // Get order details with customer information
    $order_stmt = $pdo->prepare("
        SELECT 
            o.id,
            o.order_number,
            o.total_amount,
            o.status,
            o.order_date,
            o.notes,
            o.created_at,
            o.updated_at,
            u.name as customer_name,
            u.email as customer_email,
            u.phone as customer_phone,
            u.address as customer_address
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.id
        WHERE o.id = ?
    ");
    
    $order_stmt->execute([$orderId]);
    $order = $order_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        echo json_encode([
            'success' => false,
            'error' => 'Order not found.'
        ]);
        exit;
    }
    
    // Get order items with product information
    $items_stmt = $pdo->prepare("
        SELECT 
            oi.id,
            oi.quantity,
            oi.price,
            oi.subtotal,
            p.product as product_name,
            p.category,
            p.package
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
        ORDER BY oi.id
    ");
    
    $items_stmt->execute([$orderId]);
    $items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Add items to order data
    $order['items'] = $items;
    
    // Return order details
    echo json_encode([
        'success' => true,
        'order' => $order
    ]);
    
} catch (PDOException $e) {
    error_log("Error fetching order details: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Database error occurred.'
    ]);
}
?>

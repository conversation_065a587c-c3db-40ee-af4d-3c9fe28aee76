<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in
requireLogin();

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$payment_method = $_GET['payment_method'] ?? '';

// Build WHERE clause for filtering
$where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(p.payment_date) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(p.payment_date) <= ?";
    $params[] = $date_to;
}

if (!empty($payment_method)) {
    $where_conditions[] = "p.payment_method = ?";
    $params[] = $payment_method;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // Get payments data for export
    $payments_query = "
        SELECT 
            p.transaction_id,
            p.amount,
            p.payment_method,
            p.status,
            p.payment_date,
            p.reference_number,
            u.name as customer_name,
            u.email as customer_email,
            o.order_number,
            p.notes
        FROM payments p
        LEFT JOIN users u ON p.customer_id = u.id
        LEFT JOIN orders o ON p.order_id = o.id
        $where_clause
        ORDER BY p.payment_date DESC
    ";
    
    $payments_stmt = $pdo->prepare($payments_query);
    $payments_stmt->execute($params);
    $payments = $payments_stmt->fetchAll();

    // Set headers for CSV download
    $filename = 'payments_export_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');

    // Create file pointer connected to the output stream
    $output = fopen('php://output', 'w');

    // Add CSV headers
    fputcsv($output, [
        'Transaction ID',
        'Date',
        'Customer Name',
        'Customer Email',
        'Order Number',
        'Amount',
        'Payment Method',
        'Status',
        'Reference Number',
        'Notes'
    ]);

    // Add data rows
    foreach ($payments as $payment) {
        $method_display = [
            'credit_card' => 'Credit Card',
            'paypal' => 'PayPal',
            'bank_transfer' => 'Bank Transfer',
            'cash' => 'Cash',
            'mobile_money' => 'Mobile Money'
        ];
        $method_text = $method_display[$payment['payment_method']] ?? ucfirst(str_replace('_', ' ', $payment['payment_method']));
        
        fputcsv($output, [
            $payment['transaction_id'],
            date('Y-m-d H:i:s', strtotime($payment['payment_date'])),
            $payment['customer_name'] ?? 'N/A',
            $payment['customer_email'] ?? 'N/A',
            $payment['order_number'] ?? 'N/A',
            number_format($payment['amount'], 2),
            $method_text,
            ucfirst($payment['status']),
            $payment['reference_number'] ?? '',
            $payment['notes'] ?? ''
        ]);
    }

    fclose($output);
    exit;

} catch (PDOException $e) {
    // Log error and redirect back with error message
    error_log("Export error: " . $e->getMessage());
    header('Location: payments.php?error=' . urlencode('Failed to export payments data.'));
    exit;
}
?>

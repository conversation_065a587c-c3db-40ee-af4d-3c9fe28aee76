<?php
// Get current user information for display
$current_user = getCurrentUser();
$user_name = $current_user ? $current_user['name'] : 'Guest';
$user_type = $current_user ? $current_user['user_type'] : '';
$user_initials = $current_user ? strtoupper(substr($current_user['name'], 0, 2)) : 'GU';
?>

<header class="bg-white shadow">
    <div class="flex items-center justify-between px-6 py-4">
        <div class="flex items-center">
            <button @click="sidebarOpen = !sidebarOpen" class="text-gray-500 focus:outline-none lg:hidden">
                <i class="fas fa-bars text-xl"></i>
            </button>
            <h2 class="text-xl font-semibold text-gray-800 ml-2">Dashboard</h2>
        </div>
        <div class="flex items-center space-x-4">
            <button class="p-2 text-gray-600 hover:text-gray-900" title="Notifications">
                <i class="fas fa-bell"></i>
            </button>
            <div class="relative" x-data="{ dropdownOpen: false }">
                <button @click="dropdownOpen = !dropdownOpen" class="flex items-center space-x-2 focus:outline-none">
                    <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                        <span class="text-indigo-600 font-medium text-sm"><?php echo htmlspecialchars($user_initials); ?></span>
                    </div>
                    <div class="text-left">
                        <span class="text-gray-700 font-medium"><?php echo htmlspecialchars($user_name); ?></span>
                        <div class="text-xs text-gray-500"><?php echo htmlspecialchars($user_type); ?></div>
                    </div>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>

                <!-- Dropdown Menu -->
                <div x-show="dropdownOpen"
                     @click.away="dropdownOpen = false"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border">
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-user mr-2"></i>Profile
                    </a>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-cog mr-2"></i>Settings
                    </a>
                    <div class="border-t border-gray-100"></div>
                    <a href="logout.php" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>
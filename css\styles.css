/* Custom styles for the admin dashboard */

/* Sidebar styles */
.sidebar {
    transition: all 0.3s ease;
}

/* Active navigation link */
.nav-link.active {
    background-color: #4F46E5;
    color: white !important;
    border-radius: 0.375rem;
}

/* Table row hover effect */
tr:hover {
    background-color: #f9fafb;
}

/* Card hover effect */
.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Responsive sidebar */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        z-index: 40;
        height: 100vh;
    }
    
    .sidebar.active {
        left: 0;
    }
    
    .overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 30;
    }
    
    .overlay.active {
        display: block;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales - Meat Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="css/styles.css">
    <!-- Alpine.js is already included via a script tag at the bottom -->
</head>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div :class="{'translate-x-0 ease-out': sidebarOpen, '-translate-x-full ease-in': !sidebarOpen}" class="fixed inset-y-0 left-0 z-30 w-64 bg-gray-800 text-white transform transition duration-300 lg:translate-x-0 lg:static lg:inset-0">
            <div class="p-4 border-b border-gray-700 flex justify-between items-center lg:justify-start">
                <div>
                    <h1 class="text-2xl font-bold">MeatMS</h1>
                    <p class="text-sm text-gray-400">Admin Panel</p>
                </div>
                <button @click="sidebarOpen = false" class="text-gray-400 hover:text-white lg:hidden">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <nav class="mt-4">
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold">Main</div>
                <a href="index.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>
                
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Management</div>
                <a href="products.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-box mr-2"></i> Products
                </a>
                <a href="orders.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-shopping-cart mr-2"></i> Orders
                </a>
                <a href="users.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-users mr-2"></i> Users
                </a>
                <a href="vendors.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-store mr-2"></i> Vendors
                </a>
                
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Sales & Reports</div>
                <a href="sales.html" class="block px-4 py-2 bg-indigo-700 text-white rounded mx-2">
                    <i class="fas fa-chart-line mr-2"></i> Sales
                </a>
                <a href="reports.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-file-alt mr-2"></i> Reports
                </a>
            </nav>
        </div>

        <!-- Overlay for mobile sidebar -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-20 bg-black opacity-50 transition-opacity lg:hidden" x-cloak></div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = !sidebarOpen" class="text-gray-500 focus:outline-none lg:hidden">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold text-gray-800 ml-2">Sales Analytics</h2>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Search sales..." class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <button class="p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell"></i>
                        </button>
                        <div class="relative">
                            <button class="flex items-center space-x-2 focus:outline-none">
                                <img class="w-8 h-8 rounded-full" src="https://via.placeholder.com/32" alt="User">
                                <span class="text-gray-700">Admin</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Sales Dashboard</h1>
                        <p class="mt-1 text-sm text-gray-600">Track and analyze your sales performance</p>
                    </div>
                    <div class="mt-4 md:mt-0 flex items-center space-x-2">
                        <div class="relative" x-data="{ open: false, selectedPeriod: 'Select Period' }">
                            <button @click="open = !open" class="bg-white text-gray-700 px-4 py-2 rounded-lg border hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center">
                                <span x-text="selectedPeriod">Select Period</span> 
                                <i class="fas fa-chevron-down ml-2 text-xs"></i>
                            </button>
                            <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20 py-1" x-cloak>
                                <a href="#" @click="selectedPeriod = 'Today\'s Sales'; open = false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-500 hover:text-white">Today's Sales</a>
                                <a href="#" @click="selectedPeriod = 'Yesterday\'s Sales'; open = false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-500 hover:text-white">Yesterday's Sales</a>
                                <a href="#" @click="selectedPeriod = 'This Week\'s Sales'; open = false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-500 hover:text-white">This Week's Sales</a>
                                <a href="#" @click="selectedPeriod = 'This Month\'s Sales'; open = false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-500 hover:text-white">This Month's Sales</a>
                                <a href="#" @click="selectedPeriod = 'This Year\'s Sales'; open = false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-500 hover:text-white">This Year's Sales</a>
                            </div>
                        </div>
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-download mr-2"></i> Export Report
                        </button>
                    </div>
                </div>

                <!-- Sales Stats -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Total Sales</p>
                                <h3 class="text-2xl font-bold">$0.00</h3>
                                <p class="text-xs text-gray-500 mt-1">Loading...</p>
                            </div>
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-dollar-sign text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Total Orders</p>
                                <h3 class="text-2xl font-bold">0</h3>
                                <p class="text-xs text-gray-500 mt-1">Loading...</p>
                            </div>
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-shopping-cart text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Avg. Order Value</p>
                                <h3 class="text-2xl font-bold">$0.00</h3>
                                <p class="text-xs text-gray-500 mt-1">Loading...</p>
                            </div>
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-receipt text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">New Customers</p>
                                <h3 class="text-2xl font-bold">0</h3>
                                <p class="text-xs text-gray-500 mt-1">Loading...</p>
                            </div>
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-users text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="bg-white p-6 rounded-lg shadow">
                        <h3 class="text-lg font-medium mb-4">Sales Trend</h3>
                        <div class="h-80"><canvas id="salesTrendChart"></canvas></div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <h3 class="text-lg font-medium mb-4">Revenue by Category</h3>
                        <div class="h-80"><canvas id="revenueByCategoryChart"></canvas></div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium mb-4">Recent Transactions</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- Sample Row 1 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#12345</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">John Doe</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-23</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$150.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    </td>
                                </tr>
                                <!-- Sample Row 2 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#12346</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Jane Smith</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$85.50</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    </td>
                                </tr>
                                <!-- Sample Row 3 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#12347</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Mike Johnson</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$220.75</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Cancelled</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    </td>
                                </tr>
                                <!-- Add more rows as needed -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const salesTrendCtx = document.getElementById('salesTrendChart')?.getContext('2d');
            if (salesTrendCtx) {
                new Chart(salesTrendCtx, {
                    type: 'line',
                    data: {
                        labels: ['January', 'February', 'March', 'April', 'May', 'June'],
                        datasets: [{
                            label: 'Sales',
                            data: [12000, 19000, 15000, 22000, 18000, 25000],
                            borderColor: 'rgb(79, 70, 229)', // indigo-600
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            const revenueByCategoryCtx = document.getElementById('revenueByCategoryChart')?.getContext('2d');
            if (revenueByCategoryCtx) {
                new Chart(revenueByCategoryCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Beef', 'Poultry', 'Pork', 'Lamb', 'Other'],
                        datasets: [{
                            label: 'Revenue',
                            data: [12500, 9500, 7200, 4800, 2100],
                            backgroundColor: [
                                'rgb(59, 130, 246)', // blue-500
                                'rgb(239, 68, 68)',  // red-500
                                'rgb(245, 158, 11)', // amber-500
                                'rgb(16, 185, 129)', // emerald-500
                                'rgb(107, 114, 128)' // gray-500
                            ],
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        });
    </script>
</body>
</html>

<?php
/**
 * Get User Details API Endpoint
 * Returns user details in JSON format for the view user modal
 */

require_once 'database/secure_conn.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'User ID is required'
    ]);
    exit;
}

$user_id = intval($_GET['id']);

try {
    // Fetch user details securely
    $user = fetchOne(
        "SELECT id, name, username, email, phone, user_type, address, created_at, updated_at FROM users WHERE id = ?",
        [$user_id]
    );
    
    if ($user) {
        // Return user details
        echo json_encode([
            'success' => true,
            'user' => [
                'id' => $user['id'],
                'name' => htmlspecialchars($user['name']),
                'username' => htmlspecialchars($user['username']),
                'email' => htmlspecialchars($user['email']),
                'phone' => htmlspecialchars($user['phone'] ?? ''),
                'user_type' => htmlspecialchars($user['user_type']),
                'address' => htmlspecialchars($user['address'] ?? ''),
                'created_at' => $user['created_at'],
                'updated_at' => $user['updated_at']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'User not found'
        ]);
    }
    
} catch (Exception $e) {
    // Log error and return generic message
    error_log("Error fetching user details: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while fetching user details'
    ]);
}
?>
